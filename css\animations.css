/* Animations for SailorPay Improved */

/* Fade In Animation */
.fade-in {
    opacity: 0.9;
    transition: opacity 0.5s ease-in-out;
}

.fade-in.visible {
    opacity: 1;
}

/* Slide Up Animation */
.slide-up {
    opacity: 0.9;
    transform: translateY(20px);
    transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
}

.slide-up.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Slide In Left Animation */
.slide-in-left {
    opacity: 0.9;
    transform: translateX(-20px);
    transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Slide In Right Animation */
.slide-in-right {
    opacity: 0.9;
    transform: translateX(20px);
    transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Button Ripple Effect */
.button {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-effect 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-effect {
    0% {
        transform: scale(0);
        opacity: 0.3;
    }
    100% {
        transform: scale(5);
        opacity: 0;
    }
}

/* Sticky Header */
.site-header.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
    from {
        transform: translateY(-50%);
    }
    to {
        transform: translateY(0);
    }
}

/* Form Error Styles */
.error {
    border-color: #ea4335 !important;
    box-shadow: 0 0 0 3px rgba(234, 67, 53, 0.2) !important;
}

.error-message {
    color: #ea4335;
    font-size: 12px;
    margin-top: 5px;
}

/* Mobile Menu Animation */
.main-menu {
    transition: all 0.3s ease-in-out;
}

.main-menu.active {
    animation: fadeInDown 0.5s ease-in-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover Effects */
.feature-box {
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
}

.feature-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.payment-logo {
    transition: transform 0.3s ease;
}

.payment-logo:hover {
    transform: translateY(-3px);
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Rotate Animation */
.rotate {
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Bounce Animation */
.bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

/* Shake Animation */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Typing Animation */
.typing {
    overflow: hidden;
    border-right: 0.15em solid var(--primary-color);
    white-space: nowrap;
    margin: 0 auto;
    letter-spacing: 0.15em;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--primary-color);
    }
}

/* Gradient Animation */
.gradient-animation {
    background: linear-gradient(-45deg, #1a73e8, #4285f4, #34a853, #0d47a1);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Rolling Services Strip */
#services-strip {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    padding: 20px 0;
    margin: 40px 0;
    overflow: hidden;
    position: relative;
}

.services-rolling-container {
    width: 100%;
    overflow: hidden;
    position: relative;
}

.services-rolling-track {
    display: flex;
    width: 200%; /* Exactly double width for seamless loop */
    animation: rollLeft 30s linear infinite;
    gap: 20px;
    padding-left: 20px; /* Start with some padding */
}

.service-item {
    flex: 0 0 auto;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 15px 25px;
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 200px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.service-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.service-item i {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.9);
    transition: color 0.3s ease;
}

.service-item:hover i {
    color: white;
}

.service-item span {
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0.5px;
}

@keyframes rollLeft {
    0% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .service-item {
        min-width: 180px;
        padding: 12px 20px;
        gap: 10px;
    }

    .service-item span {
        font-size: 13px;
    }

    .service-item i {
        font-size: 18px;
    }

    .services-rolling-track {
        animation-duration: 25s; /* Slightly faster on mobile */
    }
}

@media (max-width: 480px) {
    .service-item {
        min-width: 160px;
        padding: 10px 18px;
    }

    .service-item span {
        font-size: 12px;
    }

    .service-item i {
        font-size: 16px;
    }
}
